{"name": "dev", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --host localhost", "build": "vue-cli-service build --mode production", "test": "vue-cli-service build --mode development", "lint": "vue-cli-service lint"}, "dependencies": {"@ckeditor/ckeditor5-alignment": "^29.0.0", "@ckeditor/ckeditor5-basic-styles": "^29.0.0", "@ckeditor/ckeditor5-block-quote": "^29.0.0", "@ckeditor/ckeditor5-build-inline": "^29.0.0", "@ckeditor/ckeditor5-code-block": "^29.0.0", "@ckeditor/ckeditor5-dev-utils": "^25.3.0", "@ckeditor/ckeditor5-dev-webpack-plugin": "^25.3.0", "@ckeditor/ckeditor5-editor-classic": "^29.0.0", "@ckeditor/ckeditor5-essentials": "^29.0.0", "@ckeditor/ckeditor5-font": "^29.0.0", "@ckeditor/ckeditor5-heading": "^29.0.0", "@ckeditor/ckeditor5-highlight": "^29.0.0", "@ckeditor/ckeditor5-horizontal-line": "^29.0.0", "@ckeditor/ckeditor5-image": "^29.0.0", "@ckeditor/ckeditor5-indent": "^29.0.0", "@ckeditor/ckeditor5-link": "^29.0.0", "@ckeditor/ckeditor5-list": "^29.0.0", "@ckeditor/ckeditor5-media-embed": "^29.0.0", "@ckeditor/ckeditor5-paragraph": "^29.0.0", "@ckeditor/ckeditor5-special-characters": "^29.0.0", "@ckeditor/ckeditor5-table": "^29.0.0", "@ckeditor/ckeditor5-theme-lark": "^29.0.0", "@ckeditor/ckeditor5-ui": "^29.0.0", "@ckeditor/ckeditor5-vue2": "^1.0.5", "@ckeditor/ckeditor5-word-count": "^29.0.0", "axios": "^0.21.1", "core-js": "^3.6.5", "dayjs": "^1.10.5", "el-table-infinite-scroll": "^1.0.10", "element-ui": "^2.15.3", "font-awesome": "^4.7.0", "hellojs": "^1.18.1", "jquery": "^3.6.0", "js-base64": "^3.6.1", "lib-flexible": "^0.3.2", "mint-ui": "^2.2.13", "moment": "^2.29.1", "nprogress": "^0.2.0", "qs": "^6.10.1", "raw-loader": "^0.5.1", "vue": "^2.6.11", "vue-cookies": "^1.8.4", "vue-full-calendar": "^2.7.0", "vue-router": "^3.0.1", "vue2-editor": "^2.10.3", "vuedraggable": "^2.24.3", "vuelidate": "^0.7.7", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^1.1.12", "css-loader": "^5.2.6", "eslint": "^6.7.2", "node-sass": "^6.0.0", "postcss": "^8.3.5", "postcss-loader": "^3.0.0", "sass-loader": "^10", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.6.11"}, "engines": {"node": "20.x"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}